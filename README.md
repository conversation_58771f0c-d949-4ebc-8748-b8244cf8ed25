# PXD
Realizing (slightly adjusting) the general component library of Vue2&3 based on Geist Design System.

- [Geist Design System](https://vercel.com/geist/introduction)
- [Figma(Community)](https://www.figma.com/design/1234567890/PXD-UI?node-id=0-1&t=1234567890-0)

[Online Preview](https://pxd-ui.netlify.app/)

> [!WARNING]
> The project is under active development and is not ready for production.

## Features

- Vue 3 Composition API
- 100% compatible with Vue2&3
- Complete tree-shaking support

## Contribution

### Dev

```shell
pnpm install

pnpm dev
```

### Build

#### Core

```shell
pnpm build
```

#### Docs

```shell
pnpm build:docs
```
