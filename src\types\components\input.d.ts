import type { HTMLAttributes } from 'vue'
import type { ComponentLabel, ComponentSizeWithXs } from '../shared/props'

export interface InputProps {
  size?: ComponentSizeWithXs
  error?: string
  min?: number | string
  max?: number | string
  align?: 'left' | 'center' | 'right'
  label?: Component<PERSON><PERSON><PERSON>
  readonly?: boolean
  disabled?: boolean
  password?: boolean
  required?: boolean
  autofocus?: boolean
  inputType?: HTMLInputElement['type']
  inputmode?: HTMLAttributes['inputmode']
  minlength?: number | string
  maxlength?: number | string
  modelValue?: ComponentLabel
  allowClear?: boolean
  placeholder?: string
  prefixStyle?: boolean
  suffixStyle?: boolean
}
