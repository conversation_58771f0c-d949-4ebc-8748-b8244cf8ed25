{
  "css.customData": [".vscode/tailwind.json"],
  "cSpell.words": [
    "antfu",
    "bumpp",
    "choicebox",
    "esno",
    "gdsi",
    "gdsicon",
    "libondev",
    "mkdist",
    "predev",
    "pxd",
    "rspack",
    "shiki",
    "shikijs",
    "tsup",
    "turborepo",
    "vite",
    "vitesse",
    "vitest"
  ],
  "javascript.preferences.importModuleSpecifierEnding": "index",
  "typescript.preferences.importModuleSpecifierEnding": "index",

  // Disable the default formatter, use eslint instead
  "prettier.enable": true,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "ngClass"
  ],
  "tailwindCSS.classFunctions": ["tw", "clsx", "twMerge"],
  "files.associations": {
    "*.css": "tailwindcss"
  },

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc"
  ],
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "tsconfig.json": "tsconfig.*.json, env.d.ts",
    "build.config.*": "jsconfig*, vitest.*, eslint.config.*, cypress.config.*, playwright.config.*",
    "package.json": "package-lock.json, pnpm*, .npmrc, .yarnrc*, yarn*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"
  }
}
