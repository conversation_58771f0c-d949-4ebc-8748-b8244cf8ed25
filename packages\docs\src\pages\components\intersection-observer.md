# IntersectionObserver
Render only when components are visible in the viewport.

> Inspiration comes from [isaact/vue-infinity - Github](https://github.com/isaact/vue-infinity){target="_blank"}

## Default
The component supports the `root/root-margin/threshold` attribute, which will be passed directly to IntersectionObserver.

```vue demo
<script setup>
import { shallowRef } from 'vue'

const isVisible = shallowRef(true)

function onVisibleChange(visible) {
  isVisible.value = visible
}

function onBeforeShow() {
  console.log('onBeforeShow')
}

function onShow() {
  console.log('onShow')
}

function onBeforeHide() {
  console.log('onBeforeHide')
}

function onHide() {
  console.log('onHide')
}
</script>

<template>
  <div>
    <span>Content is </span>
    <span class="font-semibold underline" :class="isVisible ? 'text-blue-900' : 'text-red-900'">{{ isVisible ? 'visible' : 'hidden' }}</span>
  </div>

  <div class="w-100 max-w-full h-60 overflow-auto mt-2 p-1">
    <PIntersectionObserver
      class="w-80 max-w-full h-30 rounded-md border border-dashed mb-60 mr-100 bg-background-200"
      @change="onVisibleChange"
      @before-show="onBeforeShow"
      @show="onShow"
      @before-hide="onBeforeHide"
      @hide="onHide"
     >
      <div>Hello, world!</div>
    </PIntersectionObserver>
  </div>
</template>
```

## Estimated size
Setting an estimated size can prevent large layout deviation after rendering. (After the first rendering, it will be replaced with the real size.)

```vue demo
<script setup>
import { shallowRef, defineAsyncComponent } from 'vue'

// Because the component will not be rendered initially,
// the corresponding resource file will only be loaded when it is visible.
// (You can see when the resource is loaded in the console network panel.)
const LazyLoadedComponent = defineAsyncComponent(() => import('@/components/demos/Static.vue'))

const isVisible = shallowRef(false)

function onVisibleChange(visible) {
  isVisible.value = visible
}
</script>

<template>
  <div>
    <span>Content is </span>
    <span class="font-semibold underline" :class="isVisible ? 'text-blue-900' : 'text-red-900'">{{ isVisible ? 'visible' : 'hidden' }}</span>
  </div>

  <div class="w-100 max-w-full h-60 overflow-auto mt-2 p-1">
    <PIntersectionObserver
      class="mt-60 max-w-full"
      width="100%"
      height="64px"
      @change="onVisibleChange"
     >
      <LazyLoadedComponent />
    </PIntersectionObserver>
  </div>
</template>
```

## KeepAlive
When your component switching costs a lot, you can enable `keep-alive` to cache components.

```vue demo
<script setup>
import { defineAsyncComponent } from 'vue'

const Counter = defineAsyncComponent(() => import('@/components/demos/Counter.vue'))
</script>

<template>
  <div class="w-100 max-w-full h-60 overflow-auto p-1 flex flex-col gap-2">
    <PIntersectionObserver
      height="36px"
      class="mt-60"
    >
      <PStack align="center">
        <Counter />
        <span>Without keep-alive</span>
      </PStack>
    </PIntersectionObserver>

    <PIntersectionObserver
      height="36px"
      keep-alive
    >
      <PStack align="center">
        <Counter />
        <span>With keep-alive</span>
      </PStack>
    </PIntersectionObserver>
  </div>
</template>
```

## FAQ
If it is found that the component is no longer visible, but the component is not destroyed, adding 1px padding on the outer layer of the component can solve the problem, the specific reason is temporarily unknown.
