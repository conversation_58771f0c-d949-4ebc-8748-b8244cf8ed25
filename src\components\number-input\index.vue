<script lang="ts" setup>
import MinusIcon from '@gdsicon/vue/minus'
import PlusIcon from '@gdsicon/vue/plus'
import { computed } from 'vue'
import { useModelValue } from '../../composables/useModelValue'
import { useRepeatAction } from '../../composables/useRepeatAction'
import PInput from '../input/index.vue'

interface Props {
  min?: number
  max?: number
  step?: number
  readonly?: boolean
  disabled?: boolean
  scientific?: boolean
  modelValue?: number | null
}

defineOptions({
  name: 'PNumberInput',
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
})

const props = withDefaults(
  defineProps<Props>(),
  {
    step: 1,
    min: Number.MIN_SAFE_INTEGER,
    max: Number.MAX_SAFE_INTEGER,
    readonly: false,
    disabled: false,
    scientific: true,
  },
)

const emits = defineEmits<{
  'update:modelValue': [number]
}>()

const {
  start: startIncrease,
  stop: stopIncrease,
} = useRepeatAction({
  disabled: computed(() => props.disabled),
  action: increaseValue,
})

const {
  start: startDecrease,
  stop: stopDecrease,
} = useRepeatAction({
  disabled: computed(() => props.disabled),
  action: decreaseValue,
})

const computedModelValue = useModelValue(props, emits, {
  // get: (): number => {
  //   return formatModelValue(props.modelValue)!
  // },
})

// function formatModelValue<T>(value: T): number {
//   const digitizeValue = Number(value)

//   if (Number.isNaN(digitizeValue)) {
//     return 0
//   }

//   return digitizeValue
// }

function increaseValue() {
  if (computedModelValue.value >= props.max) {
    return
  }

  computedModelValue.value += props.step
}

function decreaseValue() {
  if (computedModelValue.value <= props.min) {
    return
  }

  computedModelValue.value -= props.step
}

// const INTEGER_REGEX = /^-?\d+$/
// const INTEGER_REGEX_WITH_SCIENTIFIC = /^-?\d+\.?\d*(e-?\d+)?$/

function onInputKeydown(ev: KeyboardEvent) {
  if (ev.key === 'ArrowUp') {
    ev.preventDefault()
    increaseValue()
  } else if (ev.key === 'ArrowDown') {
    ev.preventDefault()
    decreaseValue()
  }
}
</script>

<template>
  <PInput
    inputmode="decimal"
    :min="min"
    :max="max"
    align="center"
    v-bind="$attrs"
    :disabled="disabled"
    :readonly="readonly"
    :prefix-style="false"
    :suffix-style="false"
    :model-value="computedModelValue"
    @keydown="onInputKeydown"
  >
    <template #prefix>
      <button
        class="flex aspect-square h-full appearance-none items-center justify-center text-foreground-secondary outline-none hover:bg-background-hover hover:text-gray-1000 active:bg-background-active motion-safe:transition-colors"
        :disabled="disabled"
        @pointerdown="startDecrease"
        @pointercancel="stopDecrease"
        @pointerup="stopDecrease"
      >
        <MinusIcon class="pointer-events-none" />
      </button>
    </template>
    <template #suffix>
      <button
        class="flex aspect-square h-full appearance-none items-center justify-center text-foreground-secondary outline-none hover:bg-background-hover hover:text-gray-1000 active:bg-background-active motion-safe:transition-colors"
        :disabled="disabled"
        @pointerdown="startIncrease"
        @pointercancel="stopIncrease"
        @pointerup="stopIncrease"
      >
        <PlusIcon class="pointer-events-none" />
      </button>
    </template>
  </PInput>
</template>
